import pygame
import sys
import os
import random

# Initialize pygame
pygame.init()

# Set up the display
WINDOW_WIDTH = 600
WINDOW_HEIGHT = 400
screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
pygame.display.set_caption("Top Down Shooter")

# Define colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)

# Load spritesheet
spritesheet_path = os.path.join("Assets", "spritesheet.png")
spritesheet = pygame.image.load(spritesheet_path)

# Character sprite dimensions and position in spritesheet
SPRITE_START_X = 1
SPRITE_START_Y = 374
SPRITE_SECTION_WIDTH = 240  # 240 - 1
SPRITE_SECTION_HEIGHT = 125  # 499 - 374
SPRITE_COLS = 8  # 8 directions
SPRITE_ROWS = 3  # 3 animation frames
SPRITE_WIDTH = SPRITE_SECTION_WIDTH // SPRITE_COLS  # ~30 pixels
SPRITE_HEIGHT = SPRITE_SECTION_HEIGHT // SPRITE_ROWS  # ~42 pixels

# Function to apply color key with tolerance
def apply_color_key_with_tolerance(surface, key_color, tolerance=30):
    """Apply color key transparency with tolerance for similar colors"""
    # Convert surface to per-pixel alpha format
    surface = surface.convert_alpha()

    # Get surface dimensions
    width, height = surface.get_size()

    # Process each pixel
    for x in range(width):
        for y in range(height):
            pixel_color = surface.get_at((x, y))

            # Calculate color difference (Manhattan distance in RGB space)
            color_diff = (abs(pixel_color[0] - key_color[0]) +
                         abs(pixel_color[1] - key_color[1]) +
                         abs(pixel_color[2] - key_color[2]))

            # If color is similar enough to key color, make it transparent
            if color_diff <= tolerance:
                surface.set_at((x, y), (pixel_color[0], pixel_color[1], pixel_color[2], 0))

    return surface

# Function to check if a position collides with a tree
def check_tree_collision(x, y, width, height):
    """Check if a rectangle collides with any tree positions"""
    # Get the grid positions that the character rectangle occupies
    left_grid = x // TILE_SIZE
    right_grid = (x + width - 1) // TILE_SIZE
    top_grid = y // TILE_SIZE
    bottom_grid = (y + height - 1) // TILE_SIZE

    # Check all grid positions the character occupies
    for grid_x in range(left_grid, right_grid + 1):
        for grid_y in range(top_grid, bottom_grid + 1):
            if (grid_x, grid_y) in tree_grid_positions:
                return True
    return False

# Extract character sprites
character_sprites = []
for row in range(SPRITE_ROWS):
    sprite_row = []
    for col in range(SPRITE_COLS):
        x = SPRITE_START_X + col * SPRITE_WIDTH
        y = SPRITE_START_Y + row * SPRITE_HEIGHT
        sprite_rect = pygame.Rect(x, y, SPRITE_WIDTH, SPRITE_HEIGHT)
        sprite = spritesheet.subsurface(sprite_rect).copy()

        # Set alpha channel using corner pixel color with tolerance
        corner_color = sprite.get_at((0, 0))  # Get top-left corner pixel color
        sprite = apply_color_key_with_tolerance(sprite, corner_color, tolerance=30)

        sprite_row.append(sprite)
    character_sprites.append(sprite_row)

# Character position and state
character_x = WINDOW_WIDTH // 2 - SPRITE_WIDTH // 2
character_y = WINDOW_HEIGHT // 2 - SPRITE_HEIGHT // 2
character_direction = 4  # Down direction (0=up, going clockwise, so 4=down)
character_frame = 1  # Stationary state (middle row)
character_speed = 3  # Movement speed in pixels per frame

# Animation variables
animation_timer = 0
animation_speed = 10  # Frames between animation changes
current_animation_frame = 0  # 0 for row 1, 1 for row 3 when moving

# Direction mapping for WASD keys (clockwise from up)
# 0=up, 1=up-right, 2=right, 3=down-right, 4=down, 5=down-left, 6=left, 7=up-left
DIRECTION_UP = 0
DIRECTION_RIGHT = 2
DIRECTION_DOWN = 4
DIRECTION_LEFT = 6

# Load grass tileset
grass_tileset_path = os.path.join("Assets", "Grass.png")
grass_tileset = pygame.image.load(grass_tileset_path)

# Tile dimensions
TILE_SIZE = 16
TILES_PER_ROW = grass_tileset.get_width() // TILE_SIZE
TILES_PER_COL = grass_tileset.get_height() // TILE_SIZE

# Extract grass tiles (all columns except the last one)
grass_tiles = []
for row in range(TILES_PER_COL):
    for col in range(TILES_PER_ROW - 1):  # Exclude last column (trees)
        x = col * TILE_SIZE
        y = row * TILE_SIZE
        tile_rect = pygame.Rect(x, y, TILE_SIZE, TILE_SIZE)
        tile = grass_tileset.subsurface(tile_rect)
        grass_tiles.append(tile)

# Extract tree tile (top row of rightmost column)
tree_x = (TILES_PER_ROW - 1) * TILE_SIZE
tree_y = 0
tree_rect = pygame.Rect(tree_x, tree_y, TILE_SIZE, TILE_SIZE)
tree_tile = grass_tileset.subsurface(tree_rect)

# Generate background map
MAP_WIDTH = WINDOW_WIDTH // TILE_SIZE + 1  # Add 1 to cover any remainder
MAP_HEIGHT = WINDOW_HEIGHT // TILE_SIZE + 1
background_map = []
for row in range(MAP_HEIGHT):
    map_row = []
    for col in range(MAP_WIDTH):
        # Choose random grass tile
        grass_tile_index = random.randint(0, len(grass_tiles) - 1)
        map_row.append(grass_tile_index)
    background_map.append(map_row)

# Generate tree positions around the edge with gaps in the center of each edge
tree_positions = []
tree_grid_positions = set()  # Store grid positions for collision detection

# Calculate center positions for each edge (3 squares wide gap)
center_top = MAP_WIDTH // 2
center_bottom = MAP_WIDTH // 2
center_left = MAP_HEIGHT // 2
center_right = MAP_HEIGHT // 2

# Top edge (y = 0)
for x in range(MAP_WIDTH):
    if not (center_top - 1 <= x <= center_top + 1):  # Skip 3 center squares
        tree_grid_positions.add((x, 0))
        tree_positions.append((x * TILE_SIZE, 0 * TILE_SIZE))

# Bottom edge (y = MAP_HEIGHT - 1)
for x in range(MAP_WIDTH):
    if not (center_bottom - 1 <= x <= center_bottom + 1):  # Skip 3 center squares
        tree_grid_positions.add((x, MAP_HEIGHT - 1))
        tree_positions.append((x * TILE_SIZE, (MAP_HEIGHT - 1) * TILE_SIZE))

# Left edge (x = 0)
for y in range(1, MAP_HEIGHT - 1):  # Skip corners (already covered by top/bottom)
    if not (center_left - 1 <= y <= center_left + 1):  # Skip 3 center squares
        tree_grid_positions.add((0, y))
        tree_positions.append((0 * TILE_SIZE, y * TILE_SIZE))

# Right edge (x = MAP_WIDTH - 1)
for y in range(1, MAP_HEIGHT - 1):  # Skip corners (already covered by top/bottom)
    if not (center_right - 1 <= y <= center_right + 1):  # Skip 3 center squares
        tree_grid_positions.add((MAP_WIDTH - 1, y))
        tree_positions.append(((MAP_WIDTH - 1) * TILE_SIZE, y * TILE_SIZE))

# Game loop
running = True
clock = pygame.time.Clock()

print("Pygame window initialized. Use WASD to move, ESC to close.")

while running:
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                # ESC key closes the window
                print("ESC pressed - closing window")
                running = False

    # Handle continuous key presses for movement
    keys = pygame.key.get_pressed()
    moving = False

    # Store original position for collision checking
    original_x = character_x
    original_y = character_y

    # WASD movement
    if keys[pygame.K_w]:  # W - Move up
        new_y = character_y - character_speed
        if not check_tree_collision(character_x, new_y, SPRITE_WIDTH, SPRITE_HEIGHT):
            character_y = new_y
            character_direction = DIRECTION_UP
            moving = True
        else:
            character_direction = DIRECTION_UP  # Still face the direction even if blocked

    if keys[pygame.K_s]:  # S - Move down
        new_y = character_y + character_speed
        if not check_tree_collision(character_x, new_y, SPRITE_WIDTH, SPRITE_HEIGHT):
            character_y = new_y
            character_direction = DIRECTION_DOWN
            moving = True
        else:
            character_direction = DIRECTION_DOWN  # Still face the direction even if blocked

    if keys[pygame.K_a]:  # A - Move left
        new_x = character_x - character_speed
        if not check_tree_collision(new_x, character_y, SPRITE_WIDTH, SPRITE_HEIGHT):
            character_x = new_x
            character_direction = DIRECTION_LEFT
            moving = True
        else:
            character_direction = DIRECTION_LEFT  # Still face the direction even if blocked

    if keys[pygame.K_d]:  # D - Move right
        new_x = character_x + character_speed
        if not check_tree_collision(new_x, character_y, SPRITE_WIDTH, SPRITE_HEIGHT):
            character_x = new_x
            character_direction = DIRECTION_RIGHT
            moving = True
        else:
            character_direction = DIRECTION_RIGHT  # Still face the direction even if blocked

    # Keep character within screen bounds
    character_x = max(0, min(character_x, WINDOW_WIDTH - SPRITE_WIDTH))
    character_y = max(0, min(character_y, WINDOW_HEIGHT - SPRITE_HEIGHT))

    # Set animation frame based on movement
    if moving:
        # Toggle between animation frames when moving
        animation_timer += 1
        if animation_timer >= animation_speed:
            animation_timer = 0
            current_animation_frame = 1 - current_animation_frame  # Toggle between 0 and 1

        # Use rows 0 and 2 for movement animation (indices 0 and 2)
        character_frame = 0 if current_animation_frame == 0 else 2
    else:
        character_frame = 1  # Use second row (index 1) for stationary state
        animation_timer = 0  # Reset animation timer when not moving

    # Fill the screen with black
    screen.fill(BLACK)

    # Draw background grass tiles
    for row in range(MAP_HEIGHT):
        for col in range(MAP_WIDTH):
            tile_x = col * TILE_SIZE
            tile_y = row * TILE_SIZE
            # Only draw tiles that are visible on screen
            if tile_x < WINDOW_WIDTH and tile_y < WINDOW_HEIGHT:
                grass_tile_index = background_map[row][col]
                grass_tile = grass_tiles[grass_tile_index]
                screen.blit(grass_tile, (tile_x, tile_y))

    # Draw trees
    for tree_x, tree_y in tree_positions:
        if tree_x < WINDOW_WIDTH and tree_y < WINDOW_HEIGHT:
            screen.blit(tree_tile, (tree_x, tree_y))

    # Draw the character sprite
    current_sprite = character_sprites[character_frame][character_direction]
    screen.blit(current_sprite, (character_x, character_y))

    # Update the display
    pygame.display.flip()

    # Control frame rate
    clock.tick(60)

# Quit pygame
pygame.quit()
sys.exit()