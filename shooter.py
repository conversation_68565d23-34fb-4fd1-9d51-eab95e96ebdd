import pygame
import sys

# Initialize pygame
pygame.init()

# Set up the display
WINDOW_WIDTH = 600
WINDOW_HEIGHT = 400
screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
pygame.display.set_caption("Top Down Shooter")

# Define colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)

# Game loop
running = True
clock = pygame.time.Clock()

print("Pygame window initialized. Press any key to close.")

while running:
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
        elif event.type == pygame.KEYDOWN:
            # Any key press will close the window
            print(f"Key pressed: {pygame.key.name(event.key)}")
            running = False

    # Fill the screen with black
    screen.fill(BLACK)

    # Update the display
    pygame.display.flip()

    # Control frame rate
    clock.tick(60)

# Quit pygame
pygame.quit()
sys.exit()